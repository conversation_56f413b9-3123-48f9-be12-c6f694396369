import { Proyecto, ProyectoUpdate, ESTADO_COLORS } from '../types/proyecto';
import { FieldValue } from '../hooks/useInlineEdit';

/**
 * Creates a wrapper function for updating project fields with type safety
 * @param updateProjectField - The function to update project fields
 * @returns A wrapper function that handles type conversion and validation
 */
export const createUpdateProjectFieldWrapper = (
  updateProjectField: (id: string, field: keyof ProyectoUpdate, value: string | number | boolean | null) => Promise<void>
) => {
  return async (id: string, field: keyof Proyecto, value: FieldValue) => {
    // Type guard to check if field exists in ProyectoUpdate
    const isUpdateableField = (field: keyof Proyecto): field is keyof ProyectoUpdate => {
      const updateableFields: (keyof ProyectoUpdate)[] = [
        'nombre', 'descripcion', 'estado', 'prioridad', 'fecha_inicio',
        'fecha_fin_estimada', 'responsable_usuario_id', 'objetivo', 'presupuesto'
      ];
      return updateableFields.includes(field as keyof ProyectoUpdate);
    };

    if (isUpdateableField(field) && value !== undefined) {
      await updateProjectField(id, field, value as string | number | boolean | null);
    } else {
      throw new Error(`Campo no actualizable: ${String(field)}`);
    }
  };
};

/**
 * Formats a date string to Spanish locale format
 * @param dateString - The date string to format
 * @returns Formatted date string or '-' if invalid
 */
export const formatDate = (dateString?: string): string => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleDateString('es-ES');
  } catch {
    return '-';
  }
};

/**
 * Gets the appropriate color class for a project status
 * @param estado - The project status
 * @returns CSS class string for the status color
 */
export const getStatusColor = (estado: string | number | boolean | null): string => {
  if (!estado || typeof estado !== 'string') return 'bg-gray-100 text-gray-800';
  return ESTADO_COLORS[estado as keyof typeof ESTADO_COLORS] || 'bg-gray-100 text-gray-800';
};

/**
 * Calculates project progress based on completed tasks
 * @param totalTasks - Total number of tasks
 * @param completedTasks - Number of completed tasks
 * @returns Progress percentage (0-100)
 */
export const calculateProgress = (totalTasks: number, completedTasks: number): number => {
  if (totalTasks === 0) return 0;
  return Math.round((completedTasks / totalTasks) * 100);
};

/**
 * Gets the appropriate color for progress bar based on percentage
 * @param progress - Progress percentage (0-100)
 * @returns CSS class string for progress color
 */
export const getProgressColor = (progress: number): string => {
  if (progress >= 80) return 'bg-green-500';
  if (progress >= 60) return 'bg-blue-500';
  if (progress >= 40) return 'bg-yellow-500';
  if (progress >= 20) return 'bg-orange-500';
  return 'bg-red-500';
};

/**
 * Checks if a project is overdue based on estimated end date
 * @param project - The project to check
 * @returns true if project is overdue
 */
export const isProjectOverdue = (project: Proyecto): boolean => {
  if (!project.fecha_fin_estimada) return false;
  const today = new Date();
  const endDate = new Date(project.fecha_fin_estimada);
  return endDate < today && project.estado !== 'completado';
};

/**
 * Calculates days until project deadline
 * @param project - The project to check
 * @returns Number of days (negative if overdue, positive if future)
 */
export const getDaysUntilDeadline = (project: Proyecto): number | null => {
  if (!project.fecha_fin_estimada) return null;
  const today = new Date();
  const endDate = new Date(project.fecha_fin_estimada);
  const diffTime = endDate.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Gets human-readable text for project deadline
 * @param project - The project to check
 * @returns Human-readable deadline text or null
 */
export const getDeadlineText = (project: Proyecto): string | null => {
  const days = getDaysUntilDeadline(project);
  if (days === null) return null;
  
  if (days < 0) return `Vencido hace ${Math.abs(days)} días`;
  if (days === 0) return 'Vence hoy';
  if (days === 1) return 'Vence mañana';
  return `${days} días restantes`;
};

/**
 * Groups projects by a specified field
 * @param projects - Array of projects to group
 * @param groupBy - Field to group by ('estado', 'prioridad')
 * @returns Object with grouped projects
 */
export const groupProjects = (
  projects: Proyecto[], 
  groupBy: 'none' | 'estado' | 'prioridad'
): Record<string, Proyecto[]> => {
  if (groupBy === 'none') return { 'Todos los proyectos': projects };
  
  const grouped: Record<string, Proyecto[]> = {};
  
  projects.forEach(project => {
    let key = '';
    switch (groupBy) {
      case 'estado':
        key = project.estado;
        break;
      case 'prioridad':
        key = project.prioridad;
        break;
    }
    
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(project);
  });
  
  return grouped;
};
