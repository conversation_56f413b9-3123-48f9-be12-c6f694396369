import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTasks, useTaskKanban, useProyectos, useEmpresas } from '../hooks/useTasks';
import { useTasksRealtime } from '../hooks/useRealtime';
import { useInlineEdit, FieldValue } from '../hooks/useInlineEdit';
import { InlineEditableCell } from '../components/UI/InlineEditableCell';
import { TaskKanban } from '../components/Tasks/TaskKanban';
import {
  Tarea,
  TareaSummary,
  TareaFilters,
  EstadoTarea,
  PrioridadTarea,
  UrgenciaTarea,
  ESTADOS_TAREA,
  PRIORIDADES_TAREA,
  URGENCIAS_TAREA,
  ESTADO_TAREA_COLORS,
  PRIORIDAD_TAREA_COLORS,
  URGENCIA_COLORS
} from '../types/tarea';
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Calendar,
  User,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Trash2,
  Layers
} from 'lucide-react';
import { createUpdateTaskFieldWrapper, formatDate, getDaysText } from '../utils/taskUtils';

const TasksPage: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'list' | 'kanban'>('kanban');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<TareaFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [groupBy, setGroupBy] = useState<'none' | 'estado' | 'prioridad' | 'proyecto' | 'multiple'>('none');
  const [multipleGroupBy, setMultipleGroupBy] = useState<{
    primary: 'estado' | 'prioridad' | 'proyecto';
    secondary: 'estado' | 'prioridad' | 'proyecto';
  }>({
    primary: 'proyecto',
    secondary: 'prioridad'
  });

  const {
    tasks,
    loading: listLoading,
    error,
    fetchTasks,
    updateTaskField,
    deleteTask,
    setError
  } = useTasks();

  // Memoize filters to prevent infinite re-renders
  const memoizedFilters = useMemo(() => ({
    ...filters,
    search: searchTerm
  }), [filters, searchTerm]);

  const {
    kanbanBoard,
    loading: kanbanLoading,
    fetchKanban
  } = useTaskKanban(memoizedFilters);

  const { proyectos } = useProyectos();
  const { empresas } = useEmpresas();

  // Wrapper function to match expected signature
  const updateTaskFieldWrapper = createUpdateTaskFieldWrapper(updateTaskField);

  // Hook para edición in-place
  const {
    data: editableTasks,
    updateField,
    setData
  } = useInlineEdit({
    initialData: tasks,
    updateFunction: updateTaskFieldWrapper,
    onError: (error) => {
      console.error('Error updating task:', error);
    },
    onSuccess: (id, field, value) => {
      console.log(`Task ${id} field ${String(field)} updated to:`, value);
    }
  });

  // Actualizar datos editables cuando cambien las tareas
  useEffect(() => {
    if (tasks.length > 0) {
      setData(tasks);
    }
  }, [tasks, setData]);

  const loading = viewMode === 'list' ? listLoading : kanbanLoading;

  // Setup realtime updates
  useTasksRealtime(
    () => {
      if (viewMode === 'list') {
        fetchTasks(1, { ...filters, search: searchTerm }); // Cargar todas las tareas
      } else {
        fetchKanban();
      }
    },
    () => {
      if (viewMode === 'list') {
        fetchTasks(1, { ...filters, search: searchTerm }); // Cargar todas las tareas
      } else {
        fetchKanban();
      }
    }
  );

  // Apply filters when they change
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (viewMode === 'list') {
        fetchTasks(1, { ...filters, search: searchTerm }); // Cargar todas las tareas
      } else {
        fetchKanban();
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, viewMode, fetchTasks, fetchKanban]);

  const handleCreateTask = () => {
    navigate('/tareas/nueva');
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/tareas/${taskId}`);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setGroupBy('none');
  };

  const handleDeleteTask = async (taskId: string, taskTitle: string) => {
    if (window.confirm(`¿Estás seguro de que quieres eliminar la tarea "${taskTitle}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteTask(taskId);
        // Actualizar la lista local
        setData(prev => prev.filter(t => t.id !== taskId));
      } catch (error) {
        console.error('Error deleting task:', error);
        alert('Error al eliminar la tarea. Por favor, inténtalo de nuevo.');
      }
    }
  };

  // Función para agrupar tareas
  const groupTasks = (tasks: Tarea[]) => {
    if (groupBy === 'none') return { 'Todas las tareas': tasks };

    if (groupBy === 'multiple') {
      // Agrupación múltiple: primero por primary, luego por secondary
      const primaryGroups: Record<string, Tarea[]> = {};

      tasks.forEach(task => {
        let primaryKey = '';
        switch (multipleGroupBy.primary) {
          case 'estado':
            primaryKey = task.estado;
            break;
          case 'prioridad':
            primaryKey = task.prioridad;
            break;
          case 'proyecto':
            primaryKey = task.proyecto_nombre || 'Sin proyecto';
            break;
        }

        if (!primaryGroups[primaryKey]) {
          primaryGroups[primaryKey] = [];
        }
        primaryGroups[primaryKey].push(task);
      });

      // Ahora agrupar cada grupo primario por el criterio secundario
      const finalGroups: Record<string, Tarea[]> = {};

      Object.entries(primaryGroups).forEach(([primaryKey, primaryTasks]) => {
        primaryTasks.forEach(task => {
          let secondaryKey = '';
          switch (multipleGroupBy.secondary) {
            case 'estado':
              secondaryKey = task.estado;
              break;
            case 'prioridad':
              secondaryKey = task.prioridad;
              break;
            case 'proyecto':
              secondaryKey = task.proyecto_nombre || 'Sin proyecto';
              break;
          }

          const combinedKey = `${primaryKey} → ${secondaryKey}`;
          if (!finalGroups[combinedKey]) {
            finalGroups[combinedKey] = [];
          }
          finalGroups[combinedKey].push(task);
        });
      });

      return finalGroups;
    }

    // Agrupación simple
    const grouped: Record<string, Tarea[]> = {};

    tasks.forEach(task => {
      let key = '';
      switch (groupBy) {
        case 'estado':
          key = task.estado;
          break;
        case 'prioridad':
          key = task.prioridad;
          break;
        case 'proyecto':
          key = task.proyecto_nombre || 'Sin proyecto';
          break;
      }

      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(task);
    });

    return grouped;
  };

  // Utility functions are now imported from taskUtils

  // const handleStatusChange = async (taskId: string, newStatus: string) => {
  //   try {
  //     await updateTask(taskId, { estado: newStatus as any });
  //     // Refresh the appropriate view
  //     if (viewMode === 'list') {
  //       fetchTasks(currentPage, { ...filters, search: searchTerm });
  //     } else {
  //       fetchKanban();
  //     }
  //   } catch (error) {
  //     console.error('Error updating task status:', error);
  //   }
  // };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Error al cargar tareas
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              if (viewMode === 'list') {
                fetchTasks(1, { ...filters, search: searchTerm });
              } else {
                fetchKanban();
              }
            }}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tareas</h1>
            <p className="text-gray-600 mt-1">
              Organiza y gestiona todas tus tareas
            </p>
          </div>
          
          <button
            onClick={handleCreateTask}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nueva Tarea
          </button>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar tareas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* View Mode and Filters */}
            <div className="flex items-center space-x-4">
              {/* View Mode Toggle */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('kanban')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'kanban' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg transition-colors ${
                  showFilters || Object.keys(filters).length > 0
                    ? 'border-blue-300 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
                {Object.keys(filters).length > 0 && (
                  <span className="ml-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {Object.keys(filters).length}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              {/* Grouping Option */}
              {viewMode === 'list' && (
                <div className="mb-4 space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Layers className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">Agrupar por:</span>
                    </div>
                    <select
                      value={groupBy}
                      onChange={(e) => setGroupBy(e.target.value as 'none' | 'estado' | 'prioridad' | 'proyecto' | 'multiple')}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="none">Sin agrupar</option>
                      <option value="estado">Estado</option>
                      <option value="prioridad">Prioridad</option>
                      <option value="proyecto">Proyecto</option>
                      <option value="multiple">Agrupación múltiple</option>
                    </select>
                  </div>

                  {/* Multiple Grouping Options */}
                  {groupBy === 'multiple' && (
                    <div className="ml-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Agrupación primaria:
                          </label>
                          <select
                            value={multipleGroupBy.primary}
                            onChange={(e) => setMultipleGroupBy(prev => ({
                              ...prev,
                              primary: e.target.value as 'estado' | 'prioridad' | 'proyecto'
                            }))}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="proyecto">Proyecto</option>
                            <option value="estado">Estado</option>
                            <option value="prioridad">Prioridad</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Agrupación secundaria:
                          </label>
                          <select
                            value={multipleGroupBy.secondary}
                            onChange={(e) => setMultipleGroupBy(prev => ({
                              ...prev,
                              secondary: e.target.value as 'estado' | 'prioridad' | 'proyecto'
                            }))}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            <option value="prioridad">Prioridad</option>
                            <option value="estado">Estado</option>
                            <option value="proyecto">Proyecto</option>
                          </select>
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 mt-2">
                        Las tareas se agruparán primero por {multipleGroupBy.primary} y luego por {multipleGroupBy.secondary}
                      </p>
                    </div>
                  )}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estado
                  </label>
                  <select
                    value={filters.estado || ''}
                    onChange={(e) => setFilters({ ...filters, estado: (e.target.value as EstadoTarea) || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los estados</option>
                    {ESTADOS_TAREA.map((estado) => (
                      <option key={estado} value={estado}>
                        {estado}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={filters.prioridad || ''}
                    onChange={(e) => setFilters({ ...filters, prioridad: (e.target.value as PrioridadTarea) || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las prioridades</option>
                    {PRIORIDADES_TAREA.map((prioridad) => (
                      <option key={prioridad} value={prioridad}>
                        {prioridad}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Urgencia
                  </label>
                  <select
                    value={filters.urgencia || ''}
                    onChange={(e) => setFilters({ ...filters, urgencia: (e.target.value as UrgenciaTarea) || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las urgencias</option>
                    {URGENCIAS_TAREA.map((urgencia) => (
                      <option key={urgencia} value={urgencia}>
                        {urgencia}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Proyecto
                  </label>
                  <select
                    value={filters.proyecto_id || ''}
                    onChange={(e) => setFilters({ ...filters, proyecto_id: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todos los proyectos</option>
                    {proyectos.map((proyecto) => (
                      <option key={proyecto.id} value={proyecto.id}>
                        {proyecto.nombre}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Empresa
                  </label>
                  <select
                    value={filters.empresa_id || ''}
                    onChange={(e) => setFilters({ ...filters, empresa_id: e.target.value || undefined })}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Todas las empresas</option>
                    {empresas.map((empresa) => (
                      <option key={empresa.id} value={empresa.id}>
                        {empresa.nombre}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Limpiar filtros
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-sm text-gray-600">
            {viewMode === 'list'
              ? `Mostrando ${editableTasks.length} tareas`
              : `Total: ${kanbanBoard?.total_tareas || 0} tareas`
            }
          </p>
          
          {loading && (
            <div className="flex items-center text-sm text-gray-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Cargando...
            </div>
          )}
        </div>

        {/* Tasks List/Kanban */}
        {viewMode === 'list' ? (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {editableTasks.length === 0 ? (
              <div className="text-center py-12">
                <CheckCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron tareas
                </h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || Object.keys(filters).length > 0
                    ? 'Intenta ajustar los filtros de búsqueda.'
                    : 'Crea tu primera tarea para comenzar.'}
                </p>
                {!searchTerm && Object.keys(filters).length === 0 && (
                  <button
                    onClick={handleCreateTask}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Crear Tarea
                  </button>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupTasks(editableTasks)).map(([groupName, groupTasks]) => (
                  <div key={groupName}>
                    {groupBy !== 'none' && (
                      <h3 className="text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2">
                        {groupName} ({groupTasks.length})
                      </h3>
                    )}

                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Tarea
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Estado
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Prioridad
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Asignado
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Vencimiento
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Proyecto
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Acciones
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {groupTasks.map((task) => (
                            <tr
                              key={task.id}
                              className="hover:bg-gray-50 transition-colors"
                            >
                              <td className="px-6 py-4">
                                <div className="flex items-start">
                                  <div className="flex-1">
                                    <InlineEditableCell
                                      value={task.titulo}
                                      type="text"
                                      onSave={(value) => updateField(task.id, 'titulo', value)}
                                      className="text-sm font-medium text-gray-900 mb-1"
                                      placeholder="Título de la tarea"
                                      required
                                    />
                                    <InlineEditableCell
                                      value={task.descripcion || ''}
                                      type="text"
                                      onSave={(value) => updateField(task.id, 'descripcion', value)}
                                      className="text-sm text-gray-500"
                                      placeholder="Descripción de la tarea"
                                    />
                                  </div>
                                  {task.es_vencida && (
                                    <AlertTriangle className="h-4 w-4 text-red-500 ml-2 flex-shrink-0" />
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <InlineEditableCell
                                  value={task.estado}
                                  type="select"
                                  options={ESTADOS_TAREA.map(estado => ({ value: estado, label: estado }))}
                                  onSave={(value) => updateField(task.id, 'estado', value)}
                                  renderValue={(value) => (
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ESTADO_TAREA_COLORS[value as keyof typeof ESTADO_TAREA_COLORS] || 'bg-gray-100 text-gray-800'}`}>
                                      {value}
                                    </span>
                                  )}
                                />
                              </td>
                              <td className="px-6 py-4">
                                <div className="space-y-2">
                                  <InlineEditableCell
                                    value={task.prioridad}
                                    type="select"
                                    options={PRIORIDADES_TAREA.map(prioridad => ({ value: prioridad, label: prioridad }))}
                                    onSave={(value) => updateField(task.id, 'prioridad', value)}
                                    renderValue={(value) => (
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${PRIORIDAD_TAREA_COLORS[value as keyof typeof PRIORIDAD_TAREA_COLORS] || 'bg-gray-100 text-gray-800'}`}>
                                        {value}
                                      </span>
                                    )}
                                  />
                                  <InlineEditableCell
                                    value={task.urgencia}
                                    type="select"
                                    options={URGENCIAS_TAREA.map(urgencia => ({ value: urgencia, label: urgencia }))}
                                    onSave={(value) => updateField(task.id, 'urgencia', value)}
                                    renderValue={(value) => (
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${URGENCIA_COLORS[value as keyof typeof URGENCIA_COLORS] || 'bg-gray-100 text-gray-800'}`}>
                                        {value}
                                      </span>
                                    )}
                                  />
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                {task.asignado_usuario ? (
                                  <div className="flex items-center">
                                    <User className="h-4 w-4 text-gray-400 mr-2" />
                                    <span className="text-sm text-gray-900">
                                      {task.asignado_usuario.nombre}
                                    </span>
                                  </div>
                                ) : (
                                  <span className="text-sm text-gray-500">Sin asignar</span>
                                )}
                              </td>
                              <td className="px-6 py-4">
                                <InlineEditableCell
                                  value={task.fecha_vencimiento || ''}
                                  type="date"
                                  onSave={(value) => updateField(task.id, 'fecha_vencimiento', value)}
                                  renderValue={(value) => {
                                    if (!value) return <span className="text-sm text-gray-500">Sin fecha</span>;
                                    return (
                                      <div className={`flex items-center text-sm ${
                                        task.es_vencida ? 'text-red-600' : 'text-gray-900'
                                      }`}>
                                        <Calendar className="h-4 w-4 mr-2" />
                                        <div>
                                          <div>{formatDate(value)}</div>
                                          {task.dias_vencimiento !== undefined && (
                                            <div className="text-xs">
                                              {getDaysText(task.dias_vencimiento)}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    );
                                  }}
                                />
                              </td>
                              <td className="px-6 py-4">
                                <InlineEditableCell
                                  value={task.proyecto_id || ''}
                                  type="select"
                                  options={[
                                    { value: '', label: 'Sin proyecto' },
                                    ...proyectos.map(proyecto => ({ value: proyecto.id, label: proyecto.nombre }))
                                  ]}
                                  onSave={(value) => updateField(task.id, 'proyecto_id', value)}
                                  renderValue={(value) => {
                                    if (!value) return <span className="text-sm text-gray-500">Sin proyecto</span>;
                                    const proyecto = proyectos.find(p => p.id === value);
                                    return <span className="text-sm text-gray-900">{proyecto?.nombre || 'Proyecto no encontrado'}</span>;
                                  }}
                                />
                              </td>
                              <td className="px-6 py-4">
                                <div className="flex items-center space-x-2">
                                  <button
                                    onClick={() => handleTaskClick(task.id)}
                                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    title="Ver detalles"
                                  >
                                    Ver
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteTask(task.id, task.titulo);
                                    }}
                                    className="text-red-600 hover:text-red-800 p-1 rounded"
                                    title="Eliminar tarea"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          // Kanban View
          <div className="overflow-x-auto">
            {kanbanBoard ? (
              <TaskKanban
                kanbanBoard={kanbanBoard}
                onTaskClick={handleTaskClick}
                onUpdateTask={async (taskId: string, field: keyof TareaSummary, value: string | number | boolean | EstadoTarea | PrioridadTarea | UrgenciaTarea | undefined) => {
                  // Map TareaSummary fields to Tarea fields where possible
                  if (field === 'asignado_nombre') {
                    // Skip fields that don't exist in Tarea
                    return;
                  }
                  await updateField(taskId, field as keyof Tarea, value as FieldValue);
                }}
                onDeleteTask={handleDeleteTask}
              />
            ) : (
              <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
                <Grid className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Cargando vista Kanban...
                </h3>
              </div>
            )}
          </div>
        )}

        {/* Scroll indicator for List View */}
        {viewMode === 'list' && editableTasks.length > 20 && (
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              Mostrando todas las tareas ({editableTasks.length}). Usa el scroll para navegar.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TasksPage;
