import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { useVirtualScroll, usePaginatedMessages } from '../../hooks/useVirtualScroll';
import OptimizedMessageBubble from './OptimizedMessageBubble';
import { useChat } from '../../hooks/useChat';
import { memoryManager, memoryUtils } from '../../services/memoryManager';
import { PERFORMANCE_CONFIG, getOptimizedSettings } from '../../config/performance';

const ITEM_HEIGHT = 200; // Estimated average height per message
const CONTAINER_HEIGHT = 600; // Default container height

interface VirtualChatHistoryProps {
  className?: string;
}

const VirtualChatHistory: React.FC<VirtualChatHistoryProps> = ({ className }) => {
  const { currentThreadId, isProcessingMessage } = useChat();
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(CONTAINER_HEIGHT);
  const [autoScroll, setAutoScroll] = useState(true);

  // Create memory-managed cache for rendered components
  const componentCache = useMemo(() =>
    memoryManager.createCache<React.ReactElement>('message-components', 50, 300000), // 5 min TTL
    []
  );

  // Use paginated messages hook with dynamic page size
  const {
    messages,
    totalCount,
    isLoading,
    hasMore,
    loadMore,
    reset,
  } = usePaginatedMessages({
    threadId: currentThreadId,
    pageSize: 50,
    initialLoad: true,
  });

  // Get optimized settings based on conversation size
  const optimizedSettings = useMemo(() => {
    const messageCount = messages?.length || 0;
    const tokenCount = messages?.reduce((total, msg) =>
      total + (msg.content?.length || 0), 0) || 0;
    return getOptimizedSettings(messageCount, tokenCount);
  }, [messages]);

  // Virtual scrolling with dynamic settings
  const [virtualScroll, handleScroll] = useVirtualScroll({
    itemHeight: ITEM_HEIGHT,
    containerHeight,
    overscan: optimizedSettings.level === 'CRITICAL' ? 1 : 3, // Reduce overscan for large conversations
    totalItems: messages.length,
  });

  // Update container height on resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerHeight(rect.height);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Handle scroll events with throttling for performance
  const onScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;

    handleScroll(scrollTop);

    // Check if user scrolled to bottom (enable auto-scroll)
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50;
    setAutoScroll(isAtBottom);

    // Load more messages when scrolling up and near the top
    const loadThreshold = optimizedSettings.level === 'CRITICAL' ? 100 : 200;
    if (scrollTop < loadThreshold && hasMore && !isLoading) {
      loadMore();
    }
  }, [handleScroll, hasMore, isLoading, loadMore, optimizedSettings.level]);

  // Create throttled version of onScroll
  const throttledOnScroll = useMemo(
    () => memoryUtils.throttle(onScroll as (...args: unknown[]) => unknown, PERFORMANCE_CONFIG.UI.DEBOUNCE_SCROLL),
    [onScroll]
  );

  // Auto-scroll to bottom for new messages
  useEffect(() => {
    if (autoScroll && containerRef.current) {
      const container = containerRef.current;
      container.scrollTop = container.scrollHeight;
    }
  }, [messages.length, autoScroll]);

  // Reset when thread changes
  useEffect(() => {
    if (currentThreadId) {
      reset();
      setAutoScroll(true);
    }
  }, [currentThreadId, reset]);

  // Get visible messages
  const visibleMessages = useMemo(() => {
    return messages.slice(virtualScroll.startIndex, virtualScroll.endIndex + 1);
  }, [messages, virtualScroll.startIndex, virtualScroll.endIndex]);

  // Memoized message items with caching to prevent unnecessary re-renders
  const messageItems = useMemo(() => {
    return visibleMessages.map((msg, index) => {
      const actualIndex = virtualScroll.startIndex + index;
      const cacheKey = `${msg.thread_id}-${msg.message_id}-${msg.created_at}`;

      // Try to get from cache first
      let cachedComponent = componentCache.get(cacheKey);

      if (!cachedComponent) {
        // Create new component and cache it
        cachedComponent = (
          <div className="p-4">
            <OptimizedMessageBubble message={msg} />
          </div>
        );
        componentCache.set(cacheKey, cachedComponent);
      }

      return (
        <div
          key={cacheKey}
          style={{
            position: 'absolute',
            top: actualIndex * ITEM_HEIGHT,
            left: 0,
            right: 0,
            minHeight: ITEM_HEIGHT,
          }}
        >
          {cachedComponent}
        </div>
      );
    });
  }, [visibleMessages, virtualScroll.startIndex, componentCache]);

  if (!currentThreadId) {
    return (
      <div className={`flex flex-col justify-center items-center h-full text-center ${className || ''}`}>
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-md">
          <div className="text-6xl mb-4">💬</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">¡Comienza una conversación!</h3>
          <p className="text-gray-500 text-sm">Selecciona un agente y envía tu primer mensaje para comenzar.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full ${className || ''}`}>
      {/* Loading indicator */}
      {isLoading && messages.length === 0 && (
        <div className="flex justify-center items-center h-full">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
            <p className="text-gray-500 italic">Cargando historial...</p>
          </div>
        </div>
      )}

      {/* Virtual scrolling container */}
      {messages.length > 0 && (
        <div
          ref={containerRef}
          className="h-full overflow-y-auto bg-gradient-to-b from-gray-50 to-white"
          onScroll={throttledOnScroll}
        >
          {/* Load more indicator at top */}
          {isLoading && messages.length > 0 && (
            <div className="flex justify-center py-4">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                <span>Cargando más mensajes...</span>
              </div>
            </div>
          )}

          {/* Virtual scroll container */}
          <div
            style={{
              height: virtualScroll.totalHeight,
              position: 'relative',
            }}
          >
            {messageItems}
          </div>

          {/* Processing indicator */}
          {isProcessingMessage && (
            <div className="flex justify-start p-4">
              <div className="w-full max-w-4xl px-4 py-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 text-gray-700 border border-indigo-100 shadow-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-1">
                    <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce"></div>
                    <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-3 h-3 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-sm font-medium">El agente está pensando...</span>
                  <div className="ml-auto">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Message count indicator */}
      {totalCount > 0 && (
        <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          {messages.length} / {totalCount} mensajes
        </div>
      )}
    </div>
  );
};

export default VirtualChatHistory;
